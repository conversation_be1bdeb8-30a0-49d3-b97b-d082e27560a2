# Intelligent Knowledge Retrieval System - Deployment Makefile
# =============================================================

# Configuration Variables
ENV ?= dev
AWS_ACCOUNT ?= $(shell aws sts get-caller-identity --query Account --output text)
AWS_REGION ?= us-east-1
PROJECT_NAME = knowledge-retrieval-system
STACK_NAME = $(PROJECT_NAME)-$(ENV)

# VPC Configuration (override these for existing VPC)
VPC_ID ?= 
PRIVATE_SUBNET_IDS ?= 
PUBLIC_SUBNET_IDS ?= 

# Slack Configuration (set these as environment variables or in .env file)
SLACK_BOT_TOKEN ?=
SLACK_SIGNING_SECRET ?=

# Amazon Q Business Configuration
AMAZON_Q_APPLICATION_ID ?=

# Infrastructure Configuration
LAMBDA_MEMORY_SIZE ?= 512
LAMBDA_TIMEOUT ?= 30
OPENSEARCH_INSTANCE_TYPE ?= t3.small.search
DYNAMODB_BILLING_MODE ?= PAY_PER_REQUEST

# Build Directories
BUILD_DIR = build
DIST_DIR = dist

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

.PHONY: help validate-config check-aws setup-env build-layers build-images bootstrap deploy destroy clean test lint format

# Default target
help: ## Show this help message
	@echo "$(BLUE)Intelligent Knowledge Retrieval System - Deployment$(NC)"
	@echo "=================================================="
	@echo ""
	@echo "$(YELLOW)Usage:$(NC)"
	@echo "  make [target] [ENV=dev|prod] [AWS_ACCOUNT=********9] [AWS_REGION=us-east-1]"
	@echo ""
	@echo "$(YELLOW)Available targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Examples:$(NC)"
	@echo "  make deploy ENV=dev AWS_ACCOUNT=********9 VPC_ID=vpc-12345"
	@echo "  make deploy ENV=prod AWS_REGION=us-west-2"
	@echo "  make destroy ENV=dev"

validate-config: ## Validate required configuration
	@echo "$(BLUE)Validating configuration...$(NC)"
	@if [ -z "$(AWS_ACCOUNT)" ]; then \
		echo "$(RED)Error: AWS_ACCOUNT is required$(NC)"; \
		echo "$(YELLOW)Set it via: export AWS_ACCOUNT=********9 or make deploy AWS_ACCOUNT=********9$(NC)"; \
		exit 1; \
	fi
	@if [ -z "$(SLACK_BOT_TOKEN)" ]; then \
		echo "$(RED)Error: SLACK_BOT_TOKEN is required$(NC)"; \
		echo "$(YELLOW)Set it via: export SLACK_BOT_TOKEN=xoxb-your-token$(NC)"; \
		exit 1; \
	fi
	@if [ -z "$(SLACK_SIGNING_SECRET)" ]; then \
		echo "$(RED)Error: SLACK_SIGNING_SECRET is required$(NC)"; \
		echo "$(YELLOW)Set it via: export SLACK_SIGNING_SECRET=your-secret$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)✓ Configuration validated$(NC)"

check-aws: ## Check AWS credentials and permissions
	@echo "$(BLUE)Checking AWS credentials...$(NC)"
	@aws sts get-caller-identity > /dev/null || (echo "$(RED)Error: AWS credentials not configured$(NC)" && exit 1)
	@echo "$(GREEN)✓ AWS Account: $(AWS_ACCOUNT)$(NC)"
	@echo "$(GREEN)✓ AWS Region: $(AWS_REGION)$(NC)"
	@echo "$(GREEN)✓ AWS User: $$(aws sts get-caller-identity --query Arn --output text)$(NC)"

setup-env: validate-config ## Setup environment configuration files
	@echo "$(BLUE)Setting up environment configuration...$(NC)"
	@mkdir -p infrastructure/env
	@echo "# $(ENV) Environment Configuration" > infrastructure/env/.env.$(ENV)
	@echo "ENV=$(ENV)" >> infrastructure/env/.env.$(ENV)
	@echo "CDK_DEFAULT_ACCOUNT=$(AWS_ACCOUNT)" >> infrastructure/env/.env.$(ENV)
	@echo "CDK_DEFAULT_REGION=$(AWS_REGION)" >> infrastructure/env/.env.$(ENV)
	@echo "LAMBDA_MEMORY_SIZE=$(LAMBDA_MEMORY_SIZE)" >> infrastructure/env/.env.$(ENV)
	@echo "LAMBDA_TIMEOUT=$(LAMBDA_TIMEOUT)" >> infrastructure/env/.env.$(ENV)
	@echo "OPENSEARCH_INSTANCE_TYPE=$(OPENSEARCH_INSTANCE_TYPE)" >> infrastructure/env/.env.$(ENV)
	@echo "DYNAMODB_BILLING_MODE=$(DYNAMODB_BILLING_MODE)" >> infrastructure/env/.env.$(ENV)
	@echo "SLACK_BOT_TOKEN=$(SLACK_BOT_TOKEN)" >> infrastructure/env/.env.$(ENV)
	@echo "SLACK_SIGNING_SECRET=$(SLACK_SIGNING_SECRET)" >> infrastructure/env/.env.$(ENV)
	@echo "BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-********-v1:0" >> infrastructure/env/.env.$(ENV)
	@echo "EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1" >> infrastructure/env/.env.$(ENV)
	@echo "DYNAMODB_TTL_DAYS=7" >> infrastructure/env/.env.$(ENV)
	@if [ -n "$(AMAZON_Q_APPLICATION_ID)" ]; then \
		echo "AMAZON_Q_APPLICATION_ID=$(AMAZON_Q_APPLICATION_ID)" >> infrastructure/env/.env.$(ENV); \
	fi
	@if [ -n "$(VPC_ID)" ]; then \
		echo "VPC_ID=$(VPC_ID)" >> infrastructure/env/.env.$(ENV); \
	fi
	@if [ -n "$(PRIVATE_SUBNET_IDS)" ]; then \
		echo "PRIVATE_SUBNET_IDS=$(PRIVATE_SUBNET_IDS)" >> infrastructure/env/.env.$(ENV); \
	fi
	@if [ -n "$(PUBLIC_SUBNET_IDS)" ]; then \
		echo "PUBLIC_SUBNET_IDS=$(PUBLIC_SUBNET_IDS)" >> infrastructure/env/.env.$(ENV); \
	fi
	@echo "$(GREEN)✓ Environment file created: infrastructure/env/.env.$(ENV)$(NC)"

install-deps: ## Install all dependencies
	@echo "$(BLUE)Installing dependencies...$(NC)"
	@echo "$(YELLOW)Installing Python dependencies...$(NC)"
	@pip install -r requirements.txt
	@echo "$(YELLOW)Installing CDK dependencies...$(NC)"
	@cd infrastructure && npm install
	@echo "$(YELLOW)Installing Lambda dependencies...$(NC)"
	@cd lambda/reader && pip install -r requirements.txt --target .
	@cd lambda/writer && pip install -r requirements.txt --target .
	@cd lambda/ingestion && pip install -r requirements.txt --target .
	@cd lambda/layers/common && pip install -r requirements.txt --target python/lib/python3.11/site-packages/
	@echo "$(GREEN)✓ All dependencies installed$(NC)"

build-layers: ## Build Lambda layers
	@echo "$(BLUE)Building Lambda layers...$(NC)"
	@mkdir -p $(BUILD_DIR)/layers
	@echo "$(YELLOW)Building common layer...$(NC)"
	@cd lambda/layers/common && \
		mkdir -p python/lib/python3.11/site-packages && \
		pip install -r requirements.txt --target python/lib/python3.11/site-packages/ --upgrade
	@echo "$(GREEN)✓ Lambda layers built$(NC)"

build-images: ## Build Docker images for Lambda functions
	@echo "$(BLUE)Building Docker images...$(NC)"
	@echo "$(YELLOW)Building Reader Lambda Docker image...$(NC)"
	@cd lambda/reader && docker build -t $(PROJECT_NAME)-reader:latest .
	@echo "$(GREEN)✓ Docker images built$(NC)"

prepare-lambda-code: ## Prepare Lambda function code
	@echo "$(BLUE)Preparing Lambda function code...$(NC)"
	@mkdir -p $(BUILD_DIR)/lambda
	
	@echo "$(YELLOW)Preparing Writer Lambda...$(NC)"
	@rm -rf $(BUILD_DIR)/lambda/writer
	@cp -r lambda/writer $(BUILD_DIR)/lambda/
	@cd $(BUILD_DIR)/lambda/writer && pip install -r requirements.txt --target . --upgrade
	
	@echo "$(YELLOW)Preparing Ingestion Lambda...$(NC)"
	@rm -rf $(BUILD_DIR)/lambda/ingestion
	@cp -r lambda/ingestion $(BUILD_DIR)/lambda/
	@cd $(BUILD_DIR)/lambda/ingestion && pip install -r requirements.txt --target . --upgrade
	
	@echo "$(GREEN)✓ Lambda code prepared$(NC)"

build: build-layers build-images prepare-lambda-code ## Build all components

bootstrap: check-aws ## Bootstrap CDK for the AWS account and region
	@echo "$(BLUE)Bootstrapping CDK...$(NC)"
	@cd infrastructure && cdk bootstrap aws://$(AWS_ACCOUNT)/$(AWS_REGION)
	@echo "$(GREEN)✓ CDK bootstrapped for aws://$(AWS_ACCOUNT)/$(AWS_REGION)$(NC)"

synth: setup-env ## Synthesize CDK templates
	@echo "$(BLUE)Synthesizing CDK templates...$(NC)"
	@cd infrastructure && \
		set -a && source env/.env.$(ENV) && set +a && \
		cdk synth --context env=$(ENV)
	@echo "$(GREEN)✓ CDK templates synthesized$(NC)"

diff: setup-env ## Show CDK diff
	@echo "$(BLUE)Showing CDK diff...$(NC)"
	@cd infrastructure && \
		set -a && source env/.env.$(ENV) && set +a && \
		cdk diff --context env=$(ENV)

deploy: check-aws setup-env build bootstrap ## Deploy the complete stack
	@echo "$(BLUE)Deploying $(STACK_NAME) to $(AWS_ACCOUNT)/$(AWS_REGION)...$(NC)"
	@echo "$(YELLOW)Environment: $(ENV)$(NC)"
	@echo "$(YELLOW)Stack Name: $(STACK_NAME)$(NC)"
	@if [ -n "$(VPC_ID)" ]; then \
		echo "$(YELLOW)Using existing VPC: $(VPC_ID)$(NC)"; \
	fi
	@cd infrastructure && \
		set -a && source env/.env.$(ENV) && set +a && \
		cdk deploy --all --context env=$(ENV) --require-approval never
	@echo "$(GREEN)✓ Deployment completed!$(NC)"
	@echo ""
	@echo "$(BLUE)Stack Outputs:$(NC)"
	@cd infrastructure && \
		set -a && source env/.env.$(ENV) && set +a && \
		cdk deploy --all --context env=$(ENV) --outputs-file ../$(BUILD_DIR)/outputs.json --require-approval never > /dev/null 2>&1 || true
	@if [ -f $(BUILD_DIR)/outputs.json ]; then \
		echo "$(GREEN)Outputs saved to $(BUILD_DIR)/outputs.json$(NC)"; \
		cat $(BUILD_DIR)/outputs.json | jq -r 'to_entries[] | .key + ": " + (.value | to_entries[] | .key + " = " + .value)' 2>/dev/null || \
		cat $(BUILD_DIR)/outputs.json; \
	fi

deploy-dev: ## Deploy to development environment
	@$(MAKE) deploy ENV=dev

deploy-prod: ## Deploy to production environment
	@$(MAKE) deploy ENV=prod

update: setup-env ## Update existing stack without building
	@echo "$(BLUE)Updating $(STACK_NAME)...$(NC)"
	@cd infrastructure && \
		set -a && source env/.env.$(ENV) && set +a && \
		cdk deploy --all --context env=$(ENV) --require-approval never
	@echo "$(GREEN)✓ Stack updated!$(NC)"

destroy: setup-env ## Destroy the stack
	@echo "$(RED)WARNING: This will destroy the $(STACK_NAME) stack!$(NC)"
	@echo "$(YELLOW)Are you sure? This action cannot be undone.$(NC)"
	@read -p "Type 'yes' to continue: " confirm && [ "$$confirm" = "yes" ] || (echo "Aborted." && exit 1)
	@echo "$(BLUE)Destroying $(STACK_NAME)...$(NC)"
	@cd infrastructure && \
		set -a && source env/.env.$(ENV) && set +a && \
		cdk destroy --all --context env=$(ENV) --force
	@echo "$(GREEN)✓ Stack destroyed$(NC)"

outputs: ## Display stack outputs
	@echo "$(BLUE)Stack Outputs for $(STACK_NAME):$(NC)"
	@cd infrastructure && \
		set -a && source env/.env.$(ENV) && set +a && \
		aws cloudformation describe-stacks \
			--stack-name $(STACK_NAME) \
			--query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
			--output table 2>/dev/null || \
		echo "$(YELLOW)Stack not found or no outputs available$(NC)"

logs: ## Tail CloudWatch logs for Lambda functions
	@echo "$(BLUE)Tailing logs for $(ENV) environment...$(NC)"
	@echo "$(YELLOW)Available log groups:$(NC)"
	@aws logs describe-log-groups \
		--log-group-name-prefix "/aws/lambda/$(PROJECT_NAME)" \
		--query 'logGroups[*].logGroupName' \
		--output table
	@echo ""
	@echo "$(YELLOW)Use: aws logs tail <log-group-name> --follow$(NC)"

test: ## Run tests
	@echo "$(BLUE)Running tests...$(NC)"
	@cd lambda/reader && python -m pytest tests/ -v || true
	@cd lambda/writer && python -m pytest tests/ -v || true
	@cd lambda/ingestion && python -m pytest tests/ -v || true
	@cd tests && python -m pytest . -v || true
	@echo "$(GREEN)✓ Tests completed$(NC)"

lint: ## Run linting
	@echo "$(BLUE)Running linting...$(NC)"
	@flake8 lambda/ --max-line-length=88 --extend-ignore=E203,W503 || true
	@flake8 infrastructure/ --max-line-length=88 --extend-ignore=E203,W503 || true
	@echo "$(GREEN)✓ Linting completed$(NC)"

format: ## Format code
	@echo "$(BLUE)Formatting code...$(NC)"
	@black lambda/ infrastructure/ --line-length=88 || true
	@echo "$(GREEN)✓ Code formatted$(NC)"

clean: ## Clean build artifacts
	@echo "$(BLUE)Cleaning build artifacts...$(NC)"
	@rm -rf $(BUILD_DIR) $(DIST_DIR)
	@rm -rf lambda/*/site-packages lambda/*/.pytest_cache
	@rm -rf infrastructure/cdk.out infrastructure/node_modules
	@rm -rf lambda/layers/common/python
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@echo "$(GREEN)✓ Cleaned build artifacts$(NC)"

status: ## Show deployment status
	@echo "$(BLUE)Deployment Status:$(NC)"
	@echo "$(YELLOW)Environment: $(ENV)$(NC)"
	@echo "$(YELLOW)AWS Account: $(AWS_ACCOUNT)$(NC)"
	@echo "$(YELLOW)AWS Region: $(AWS_REGION)$(NC)"
	@echo "$(YELLOW)Stack Name: $(STACK_NAME)$(NC)"
	@echo ""
	@aws cloudformation describe-stacks \
		--stack-name $(STACK_NAME) \
		--query 'Stacks[0].[StackName,StackStatus,CreationTime,LastUpdatedTime]' \
		--output table 2>/dev/null || \
	echo "$(YELLOW)Stack not found$(NC)"

docs: ## Generate documentation
	@echo "$(BLUE)Generating documentation...$(NC)"
	@mkdir -p $(BUILD_DIR)/docs
	@cd infrastructure && cdk docs > ../$(BUILD_DIR)/docs/cdk-docs.md || true
	@echo "$(GREEN)✓ Documentation generated in $(BUILD_DIR)/docs/$(NC)"

# Configuration examples
example-config: ## Show example configuration
	@echo "$(BLUE)Example Configuration:$(NC)"
	@echo ""
	@echo "$(YELLOW)Environment Variables:$(NC)"
	@echo "export AWS_ACCOUNT=************"
	@echo "export AWS_REGION=us-east-1"
	@echo "export SLACK_BOT_TOKEN=xoxb-your-bot-token"
	@echo "export SLACK_SIGNING_SECRET=your-signing-secret"
	@echo "export AMAZON_Q_APPLICATION_ID=your-amazon-q-app-id"
	@echo ""
	@echo "$(YELLOW)With existing VPC:$(NC)"
	@echo "export VPC_ID=vpc-********"
	@echo "export PRIVATE_SUBNET_IDS=subnet-12345,subnet-67890"
	@echo "export PUBLIC_SUBNET_IDS=subnet-abcde,subnet-fghij"
	@echo ""
	@echo "$(YELLOW)Deploy command:$(NC)"
	@echo "make deploy ENV=dev"

# All-in-one deployment
all: clean install-deps deploy ## Clean, install dependencies, and deploy

.DEFAULT_GOAL := help 