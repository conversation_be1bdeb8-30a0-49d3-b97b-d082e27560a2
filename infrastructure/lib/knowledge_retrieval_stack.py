"""Main Knowledge Retrieval System CDK stack."""

from aws_cdk import CfnOutput, Stack
from constructs import Construct

from .stacks.compute_stack import Compute<PERSON>tack
from .stacks.monitoring_stack import MonitoringStack
from .stacks.network_stack import NetworkStack
from .stacks.storage_stack import StorageStack


class KnowledgeRetrievalStack(Stack):
    """Main stack composing all specialized stacks for Knowledge Retrieval."""

    def __init__(
        self, scope: Construct, construct_id: str, env_name: str, **kwargs
    ):
        """Initialize the main Knowledge Retrieval Stack."""
        super().__init__(scope, construct_id, **kwargs)

        # Create network stack for VPC and networking
        network_stack = NetworkStack(
            self, f"NetworkStack-{env_name}", env_name=env_name
        )

        # Create storage stack for data persistence
        storage_stack = StorageStack(
            self,
            f"StorageStack-{env_name}",
            env_name=env_name,
            vpc=network_stack.vpc,
        )

        # Create compute stack for Lambda functions and API Gateway
        compute_stack = ComputeStack(
            self,
            f"ComputeStack-{env_name}",
            env_name=env_name,
            vpc=network_stack.vpc,
            document_bucket=storage_stack.document_bucket,
            conversation_table=storage_stack.conversation_table,
            opensearch_domain=storage_stack.opensearch_domain,
            message_queue=storage_stack.message_queue,
        )

        # Create monitoring stack for CloudWatch alarms and dashboards
        MonitoringStack(
            self,
            f"MonitoringStack-{env_name}",
            env_name=env_name,
            lambda_functions=[
                compute_stack.reader_lambda,
                compute_stack.writer_lambda,
                compute_stack.ingestion_lambda,
            ],
            api_gateway=compute_stack.api_gateway,
        )

        # Outputs
        CfnOutput(
            self,
            "DynamoDBTableName",
            value=storage_stack.conversation_table.table_name,
            description="DynamoDB table for conversation history",
        )

        CfnOutput(
            self,
            "OpenSearchDomainEndpoint",
            value=storage_stack.opensearch_domain.domain_endpoint,
            description="OpenSearch domain endpoint for document search",
        )

        CfnOutput(
            self,
            "DocumentBucketName",
            value=storage_stack.document_bucket.bucket_name,
            description="S3 bucket for document storage",
        )

        CfnOutput(
            self,
            "SQSQueueUrl",
            value=storage_stack.message_queue.queue_url,
            description="SQS FIFO queue URL for message processing",
        )

        CfnOutput(
            self,
            "APIGatewayEndpoint",
            value=compute_stack.api_gateway.url,
            description="API Gateway endpoint for Slack integration",
        )

        CfnOutput(
            self,
            "SlackEventsEndpoint",
            value=(f"{compute_stack.api_gateway.url}slack/events"),
            description="Slack Events API endpoint for webhook configuration",
        )

        CfnOutput(
            self,
            "SlackCommandsEndpoint",
            value=(f"{compute_stack.api_gateway.url}slack/commands"),
            description="Slack Slash Commands endpoint for /askq command configuration",
        )

        CfnOutput(
            self,
            "TestQueryEndpoint",
            value=(f"{compute_stack.api_gateway.url}test/query"),
            description="Direct API test endpoint for testing without Slack integration",
        )
