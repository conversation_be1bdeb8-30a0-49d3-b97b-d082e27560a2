from aws_cdk import NestedStack
from aws_cdk import aws_ec2 as ec2
from constructs import Construct


class NetworkStack(NestedStack):
    """Stack for networking resources like VPC and subnets."""

    def __init__(
        self,
        scope: Construct,
        id: str,
        env_name: str,
        vpc_id: str = None,
        **kwargs,
    ):
        super().__init__(scope, id, **kwargs)

        # Get existing VPC or create a new one
        if vpc_id:
            self.vpc = ec2.Vpc.from_lookup(self, "ExistingVPC", vpc_id=vpc_id)
        else:
            # Create a new VPC if vpc_id is not provided
            self.vpc = ec2.Vpc(
                self,
                "KnowledgeRetrievalVPC",
                vpc_name=f"knowledge-retrieval-vpc-{env_name}",
                max_azs=2,
                nat_gateways=1,
                subnet_configuration=[
                    ec2.SubnetConfiguration(
                        name="Public",
                        subnet_type=ec2.SubnetType.PUBLIC,
                        cidr_mask=24,
                    ),
                    ec2.SubnetConfiguration(
                        name="Private",
                        subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                        cidr_mask=24,
                    ),
                ],
            )

        # Create security groups with least privilege access
        self._create_security_groups(env_name)

    def _create_security_groups(self, env_name: str):
        """Create security groups for Lambda functions with minimal required access."""

        # Lambda Security Group - Outbound only for AWS services
        self.lambda_security_group = ec2.SecurityGroup(
            self,
            "LambdaSecurityGroup",
            vpc=self.vpc,
            description=f"Security group for Lambda functions - {env_name}",
            security_group_name=f"lambda-sg-{env_name}",
            allow_all_outbound=False,  # We'll add specific rules
        )

        # HTTPS outbound for AWS services (443)
        self.lambda_security_group.add_egress_rule(
            peer=ec2.Peer.any_ipv4(),
            connection=ec2.Port.tcp(443),
            description="HTTPS outbound for AWS services (Bedrock, DynamoDB, S3, SQS)"
        )

        # OpenSearch Security Group - Only Lambda access
        self.opensearch_security_group = ec2.SecurityGroup(
            self,
            "OpenSearchSecurityGroup",
            vpc=self.vpc,
            description=f"Security group for OpenSearch cluster - {env_name}",
            security_group_name=f"opensearch-sg-{env_name}",
            allow_all_outbound=False,
        )

        # Allow Lambda to access OpenSearch on port 443
        self.opensearch_security_group.add_ingress_rule(
            peer=ec2.Peer.security_group_id(self.lambda_security_group.security_group_id),
            connection=ec2.Port.tcp(443),
            description="Allow Lambda functions to access OpenSearch"
        )

        # VPC Endpoint Security Group - For AWS services
        self.vpc_endpoint_security_group = ec2.SecurityGroup(
            self,
            "VPCEndpointSecurityGroup",
            vpc=self.vpc,
            description=f"Security group for VPC endpoints - {env_name}",
            security_group_name=f"vpc-endpoint-sg-{env_name}",
            allow_all_outbound=False,
        )

        # Allow Lambda to access VPC endpoints on port 443
        self.vpc_endpoint_security_group.add_ingress_rule(
            peer=ec2.Peer.security_group_id(self.lambda_security_group.security_group_id),
            connection=ec2.Port.tcp(443),
            description="Allow Lambda functions to access VPC endpoints"
        )

        # Create VPC endpoints for AWS services to avoid internet traffic
        self._create_vpc_endpoints()

    def _create_vpc_endpoints(self):
        """Create VPC endpoints for AWS services to keep traffic within AWS network."""

        # S3 Gateway Endpoint (no additional cost)
        self.vpc.add_gateway_endpoint(
            "S3GatewayEndpoint",
            service=ec2.GatewayVpcEndpointAwsService.S3,
            subnets=[ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS)]
        )

        # DynamoDB Gateway Endpoint (no additional cost)
        self.vpc.add_gateway_endpoint(
            "DynamoDBGatewayEndpoint",
            service=ec2.GatewayVpcEndpointAwsService.DYNAMODB,
            subnets=[ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS)]
        )

        # SQS Interface Endpoint
        self.vpc.add_interface_endpoint(
            "SQSInterfaceEndpoint",
            service=ec2.InterfaceVpcEndpointAwsService.SQS,
            subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
            security_groups=[self.vpc_endpoint_security_group]
        )

        # Bedrock Interface Endpoint (if available in region)
        try:
            self.vpc.add_interface_endpoint(
                "BedrockInterfaceEndpoint",
                service=ec2.InterfaceVpcEndpointAwsService.BEDROCK,
                subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
                security_groups=[self.vpc_endpoint_security_group]
            )
        except:
            # Bedrock VPC endpoint might not be available in all regions
            pass
