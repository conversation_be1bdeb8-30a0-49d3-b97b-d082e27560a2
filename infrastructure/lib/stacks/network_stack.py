import os
from typing import List, Optional
from aws_cdk import NestedStack
from aws_cdk import aws_ec2 as ec2
from constructs import Construct


class NetworkStack(NestedStack):
    """Stack for networking resources like VPC and subnets."""

    def __init__(
        self,
        scope: Construct,
        id: str,
        env_name: str,
        vpc_id: str = None,
        existing_lambda_sg_id: str = None,
        existing_opensearch_sg_id: str = None,
        existing_vpc_endpoint_sg_id: str = None,
        existing_vpc_endpoint_ids: str = None,  # Comma-separated list
        **kwargs,
    ):
        super().__init__(scope, id, **kwargs)

        # Store configuration
        self.env_name = env_name
        self.existing_lambda_sg_id = existing_lambda_sg_id
        self.existing_opensearch_sg_id = existing_opensearch_sg_id
        self.existing_vpc_endpoint_sg_id = existing_vpc_endpoint_sg_id
        self.existing_vpc_endpoint_ids = existing_vpc_endpoint_ids.split(',') if existing_vpc_endpoint_ids else []

        # Get existing VPC or create a new one
        if vpc_id:
            self.vpc = ec2.Vpc.from_lookup(self, "ExistingVPC", vpc_id=vpc_id)
        else:
            # Create a new VPC if vpc_id is not provided
            self.vpc = ec2.Vpc(
                self,
                "KnowledgeRetrievalVPC",
                vpc_name=f"knowledge-retrieval-vpc-{env_name}",
                max_azs=2,
                nat_gateways=1,
                subnet_configuration=[
                    ec2.SubnetConfiguration(
                        name="Public",
                        subnet_type=ec2.SubnetType.PUBLIC,
                        cidr_mask=24,
                    ),
                    ec2.SubnetConfiguration(
                        name="Private",
                        subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                        cidr_mask=24,
                    ),
                ],
            )

        # Create or import security groups
        self._setup_security_groups()

        # Create or skip VPC endpoints based on existing resources
        self._setup_vpc_endpoints()

    def _setup_security_groups(self):
        """Create or import security groups based on configuration."""

        # Lambda Security Group
        if self.existing_lambda_sg_id:
            self.lambda_security_group = ec2.SecurityGroup.from_security_group_id(
                self, "ExistingLambdaSecurityGroup", self.existing_lambda_sg_id
            )
            print(f"Using existing Lambda security group: {self.existing_lambda_sg_id}")
        else:
            self.lambda_security_group = ec2.SecurityGroup(
                self,
                "LambdaSecurityGroup",
                vpc=self.vpc,
                description=f"Security group for Lambda functions - {self.env_name}",
                security_group_name=f"lambda-sg-{self.env_name}",
                allow_all_outbound=False,
            )

            # Add HTTPS outbound for AWS services (443)
            self.lambda_security_group.add_egress_rule(
                peer=ec2.Peer.any_ipv4(),
                connection=ec2.Port.tcp(443),
                description="HTTPS outbound for AWS services (Bedrock, DynamoDB, S3, SQS)"
            )

            # Only add VPC endpoint access if we're creating new VPC endpoints
            if not self.existing_vpc_endpoint_ids:
                # We'll add VPC endpoint access rules later if needed
                pass

        # OpenSearch Security Group
        if self.existing_opensearch_sg_id:
            self.opensearch_security_group = ec2.SecurityGroup.from_security_group_id(
                self, "ExistingOpenSearchSecurityGroup", self.existing_opensearch_sg_id
            )
            print(f"Using existing OpenSearch security group: {self.existing_opensearch_sg_id}")
        else:
            self.opensearch_security_group = ec2.SecurityGroup(
                self,
                "OpenSearchSecurityGroup",
                vpc=self.vpc,
                description=f"Security group for OpenSearch cluster - {self.env_name}",
                security_group_name=f"opensearch-sg-{self.env_name}",
                allow_all_outbound=False,
            )

            # Allow Lambda to access OpenSearch on port 443
            self.opensearch_security_group.add_ingress_rule(
                peer=ec2.Peer.security_group_id(self.lambda_security_group.security_group_id),
                connection=ec2.Port.tcp(443),
                description="Allow Lambda functions to access OpenSearch"
            )

        # VPC Endpoint Security Group (only if we're creating new endpoints)
        if self.existing_vpc_endpoint_sg_id:
            self.vpc_endpoint_security_group = ec2.SecurityGroup.from_security_group_id(
                self, "ExistingVPCEndpointSecurityGroup", self.existing_vpc_endpoint_sg_id
            )
            print(f"Using existing VPC endpoint security group: {self.existing_vpc_endpoint_sg_id}")
        elif not self.existing_vpc_endpoint_ids:
            # Only create VPC endpoint SG if we're creating new VPC endpoints
            self.vpc_endpoint_security_group = ec2.SecurityGroup(
                self,
                "VPCEndpointSecurityGroup",
                vpc=self.vpc,
                description=f"Security group for VPC endpoints - {self.env_name}",
                security_group_name=f"vpc-endpoint-sg-{self.env_name}",
                allow_all_outbound=False,
            )

            # Allow Lambda to access VPC endpoints on port 443
            self.vpc_endpoint_security_group.add_ingress_rule(
                peer=ec2.Peer.security_group_id(self.lambda_security_group.security_group_id),
                connection=ec2.Port.tcp(443),
                description="Allow Lambda functions to access VPC endpoints"
            )
        else:
            # Using existing VPC endpoints, no need for new VPC endpoint security group
            self.vpc_endpoint_security_group = None

    def _setup_vpc_endpoints(self):
        """Create VPC endpoints only if not using existing ones."""

        if self.existing_vpc_endpoint_ids:
            print(f"Using existing VPC endpoints: {', '.join(self.existing_vpc_endpoint_ids)}")
            print("Skipping VPC endpoint creation to avoid duplication and reduce costs.")
            return

        print("Creating new VPC endpoints for AWS services...")

        # S3 Gateway Endpoint (no additional cost)
        self.vpc.add_gateway_endpoint(
            "S3GatewayEndpoint",
            service=ec2.GatewayVpcEndpointAwsService.S3,
            subnets=[ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS)]
        )

        # DynamoDB Gateway Endpoint (no additional cost)
        self.vpc.add_gateway_endpoint(
            "DynamoDBGatewayEndpoint",
            service=ec2.GatewayVpcEndpointAwsService.DYNAMODB,
            subnets=[ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS)]
        )

        # Only create interface endpoints if we have a security group for them
        if self.vpc_endpoint_security_group:
            # SQS Interface Endpoint
            self.vpc.add_interface_endpoint(
                "SQSInterfaceEndpoint",
                service=ec2.InterfaceVpcEndpointAwsService.SQS,
                subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
                security_groups=[self.vpc_endpoint_security_group]
            )

            # Bedrock Interface Endpoint (if available in region)
            try:
                self.vpc.add_interface_endpoint(
                    "BedrockInterfaceEndpoint",
                    service=ec2.InterfaceVpcEndpointAwsService.BEDROCK,
                    subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
                    security_groups=[self.vpc_endpoint_security_group]
                )
            except:
                # Bedrock VPC endpoint might not be available in all regions
                print("Bedrock VPC endpoint not available in this region, skipping...")
                pass
        else:
            print("No VPC endpoint security group available, skipping interface endpoints...")

    def has_existing_vpc_endpoints(self) -> bool:
        """Check if using existing VPC endpoints."""
        return bool(self.existing_vpc_endpoint_ids)

    def get_vpc_endpoint_info(self) -> dict:
        """Get information about VPC endpoint configuration."""
        return {
            "using_existing_endpoints": self.has_existing_vpc_endpoints(),
            "existing_endpoint_ids": self.existing_vpc_endpoint_ids,
            "vpc_endpoint_security_group_id": getattr(self.vpc_endpoint_security_group, 'security_group_id', None),
            "cost_optimization": "Using existing VPC endpoints" if self.has_existing_vpc_endpoints() else "Creating new VPC endpoints"
        }
