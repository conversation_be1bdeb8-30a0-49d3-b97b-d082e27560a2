import os

from aws_cdk import Duration, NestedStack
from aws_cdk import aws_apigateway as apigateway
from aws_cdk import aws_dynamodb as dynamodb
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_ecr_assets as ecr_assets
from aws_cdk import aws_iam as iam
from aws_cdk import aws_lambda as lambda_
from aws_cdk import aws_logs as logs
from aws_cdk import aws_opensearchservice as opensearch
from aws_cdk import aws_s3 as s3
from aws_cdk import aws_sqs as sqs
from constructs import Construct


class ComputeStack(NestedStack):
    """Stack for compute resources like Lambda functions and API Gateway."""

    def __init__(
        self,
        scope: Construct,
        id: str,
        env_name: str,
        vpc: ec2.IVpc,
        document_bucket: s3.IBucket,
        conversation_table: dynamodb.ITable,
        opensearch_domain: opensearch.IDomain,
        message_queue: sqs.IQueue,
        **kwargs,
    ):
        super().__init__(scope, id, **kwargs)

        # Environment variables
        lambda_memory = int(os.environ.get("LAMBDA_MEMORY_SIZE", "512"))
        lambda_timeout = int(os.environ.get("LAMBDA_TIMEOUT", "30"))
        dynamodb_ttl_days = int(os.environ.get("DYNAMODB_TTL_DAYS", "7"))
        bedrock_model_id = os.environ.get(
            "BEDROCK_MODEL_ID", "anthropic.claude-3-sonnet-20240229-v1:0"
        )
        embedding_model_id = os.environ.get(
            "EMBEDDING_MODEL_ID", "amazon.titan-embed-text-v1"
        )
        slack_bot_token = os.environ.get("SLACK_BOT_TOKEN", "")
        slack_signing_secret = os.environ.get("SLACK_SIGNING_SECRET", "")
        amazon_q_application_id = os.environ.get("AMAZON_Q_APPLICATION_ID", "")

        # Lambda IAM Role with necessary permissions
        lambda_role = iam.Role(
            self,
            "LambdaExecutionRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                ),
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "AmazonBedrockFullAccess"
                ),
            ],
        )

        # Add specific permissions
        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "dynamodb:GetItem",
                    "dynamodb:PutItem",
                    "dynamodb:Query",
                    "dynamodb:Scan",
                    "dynamodb:UpdateItem",
                    "dynamodb:DeleteItem",
                ],
                resources=[conversation_table.table_arn],
            )
        )

        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "es:ESHttpGet",
                    "es:ESHttpPost",
                    "es:ESHttpPut",
                    "es:ESHttpDelete",
                ],
                resources=[f"{opensearch_domain.domain_arn}/*"],
            )
        )

        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "sqs:SendMessage",
                    "sqs:ReceiveMessage",
                    "sqs:DeleteMessage",
                    "sqs:GetQueueAttributes",
                ],
                resources=[message_queue.queue_arn],
            )
        )

        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=["s3:GetObject", "s3:PutObject", "s3:ListBucket"],
                resources=[
                    document_bucket.bucket_arn,
                    f"{document_bucket.bucket_arn}/*",
                ],
            )
        )

        # Add Amazon Q Business permissions
        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "qbusiness:Chat",
                    "qbusiness:GetApplication",
                    "qbusiness:ListApplications",
                ],
                resources=["*"],  # Amazon Q Business requires wildcard for some actions
            )
        )

        # Lambda Layer for shared code
        common_layer = lambda_.LayerVersion(
            self,
            "CommonLayer",
            code=lambda_.Code.from_asset("../lambda/layers/common"),
            compatible_runtimes=[lambda_.Runtime.PYTHON_3_11],
            description="Common utilities and dependencies",
        )

        # Reader Lambda Function (Slack Bolt) - Using Container Image
        reader_docker_asset = ecr_assets.DockerImageAsset(
            self,
            "ReaderDockerImage",
            directory="../lambda/reader",
            file="Dockerfile",
        )

        self.reader_lambda = lambda_.DockerImageFunction(
            self,
            "ReaderLambda",
            function_name=f"knowledge-retrieval-reader-{env_name}",
            code=lambda_.DockerImageCode.from_ecr(
                repository=reader_docker_asset.repository,
                tag=reader_docker_asset.asset_hash,
            ),
            memory_size=lambda_memory,
            timeout=Duration.seconds(lambda_timeout),
            environment={
                "QUEUE_URL": message_queue.queue_url,
                "ENVIRONMENT": env_name,
                "SLACK_BOT_TOKEN": slack_bot_token,
                "SLACK_SIGNING_SECRET": slack_signing_secret,
            },
            role=lambda_role,
            log_retention=logs.RetentionDays.ONE_WEEK,
        )

        # Writer Lambda Function
        self.writer_lambda = lambda_.Function(
            self,
            "WriterLambda",
            function_name=f"knowledge-retrieval-writer-{env_name}",
            runtime=lambda_.Runtime.PYTHON_3_11,
            code=lambda_.Code.from_asset("../lambda/writer"),
            handler="app.handler",
            memory_size=lambda_memory * 2,  # Double memory for the writer
            timeout=Duration.seconds(lambda_timeout * 2),  # Double timeout
            environment={
                "DYNAMODB_TABLE": conversation_table.table_name,
                "OPENSEARCH_DOMAIN": opensearch_domain.domain_endpoint,
                "BEDROCK_MODEL_ID": bedrock_model_id,
                "EMBEDDING_MODEL_ID": embedding_model_id,
                "TTL_DAYS": str(dynamodb_ttl_days),
                "ENVIRONMENT": env_name,
                "AMAZON_Q_APPLICATION_ID": amazon_q_application_id,
                "SLACK_BOT_TOKEN": slack_bot_token,
            },
            role=lambda_role,
            layers=[common_layer],
            log_retention=logs.RetentionDays.ONE_WEEK,
        )

        # Ingestion Lambda Function
        self.ingestion_lambda = lambda_.Function(
            self,
            "IngestionLambda",
            function_name=f"knowledge-retrieval-ingestion-{env_name}",
            runtime=lambda_.Runtime.PYTHON_3_11,
            code=lambda_.Code.from_asset("../lambda/ingestion"),
            handler="app.handler",
            memory_size=lambda_memory * 2,
            timeout=Duration.seconds(300),  # 5 minutes for document processing
            environment={
                "OPENSEARCH_DOMAIN": opensearch_domain.domain_endpoint,
                "DOCUMENT_BUCKET": document_bucket.bucket_name,
                "EMBEDDING_MODEL_ID": embedding_model_id,
                "ENVIRONMENT": env_name,
            },
            role=lambda_role,
            layers=[common_layer],
            log_retention=logs.RetentionDays.ONE_WEEK,
        )

        # SQS Trigger for Writer Lambda
        self.writer_lambda.add_event_source_mapping(
            "SQSEventSource",
            batch_size=1,
            event_source_arn=message_queue.queue_arn,
        )

        # S3 Trigger for Ingestion Lambda
        self.ingestion_lambda.add_event_source(
            lambda_.S3EventSource(
                document_bucket,
                events=[s3.EventType.OBJECT_CREATED],
                filters=[s3.NotificationKeyFilter(prefix="uploads/")],
            )
        )

        # API Gateway for Reader Lambda (replacing ALB)
        self.api_gateway = apigateway.RestApi(
            self,
            "KnowledgeRetrievalAPI",
            rest_api_name=f"knowledge-retrieval-api-{env_name}",
            description="API Gateway for Slack Knowledge Retrieval System",
            deploy_options=apigateway.StageOptions(
                stage_name="v1",
                logging_level=apigateway.MethodLoggingLevel.INFO,
                data_trace_enabled=True,
                metrics_enabled=True,
            ),
            cloud_watch_role=True,
            endpoint_configuration=apigateway.EndpointConfiguration(
                types=[apigateway.EndpointType.REGIONAL]
            ),
        )

        # Lambda Integration for Reader Function
        reader_integration = apigateway.LambdaIntegration(
            self.reader_lambda,
            request_templates={
                "application/json": (
                    """
                {
                    "body": $input.json('$'),
                    "headers": {
                        #foreach($header in $input.params().header.keySet())
                        "$header": "$util.escapeJavaScript("""
                    """$input.params().header.get($header))"
                        #if($foreach.hasNext),#end
                        #end
                    },
                    "httpMethod": "$context.httpMethod",
                    "isBase64Encoded": false,
                    "path": "$context.path",
                    "pathParameters": {
                        #foreach($param in $input.params().path.keySet())
                        "$param": "$util.escapeJavaScript("""
                    """$input.params().path.get($param))"
                        #if($foreach.hasNext),#end
                        #end
                    },
                    "queryStringParameters": {
                        #foreach($param in $input.params().querystring.keySet())
                        "$param": "$util.escapeJavaScript("""
                    """$input.params().querystring.get($param))"
                        #if($foreach.hasNext),#end
                        #end
                    },
                    "requestContext": {
                        "requestId": "$context.requestId",
                        "identity": {
                            "sourceIp": "$context.identity.sourceIp",
                            "userAgent": "$context.identity.userAgent"
                        }
                    }
                }
                """
                )
            },
            proxy=False,
            allow_test_invoke=True,
        )

        # Create /slack resource for Slack events
        slack_resource = self.api_gateway.root.add_resource("slack")

        # Add events endpoint for Slack events
        events_resource = slack_resource.add_resource("events")
        events_resource.add_method(
            "POST",
            reader_integration,
            method_responses=[
                apigateway.MethodResponse(
                    status_code="200",
                    response_models={
                        "application/json": apigateway.Model.EMPTY_MODEL
                    },
                ),
                apigateway.MethodResponse(
                    status_code="400",
                    response_models={
                        "application/json": apigateway.Model.ERROR_MODEL
                    },
                ),
                apigateway.MethodResponse(
                    status_code="500",
                    response_models={
                        "application/json": apigateway.Model.ERROR_MODEL
                    },
                ),
            ],
        )

        # Add interactive endpoint for Slack interactive components
        interactive_resource = slack_resource.add_resource("interactive")
        interactive_resource.add_method(
            "POST",
            reader_integration,
            method_responses=[
                apigateway.MethodResponse(
                    status_code="200",
                    response_models={
                        "application/json": apigateway.Model.EMPTY_MODEL
                    },
                )
            ],
        )

        # Add commands endpoint for Slack slash commands
        commands_resource = slack_resource.add_resource("commands")
        commands_resource.add_method(
            "POST",
            reader_integration,
            method_responses=[
                apigateway.MethodResponse(
                    status_code="200",
                    response_models={
                        "application/json": apigateway.Model.EMPTY_MODEL
                    },
                )
            ],
        )

        # Grant API Gateway permission to invoke Lambda
        self.reader_lambda.add_permission(
            "APIGatewayInvokePermission",
            principal=iam.ServicePrincipal("apigateway.amazonaws.com"),
            action="lambda:InvokeFunction",
            source_arn=(f"{self.api_gateway.arn_for_execute_api()}/*/*/*"),
        )
