#!/bin/bash

# Script to discover existing VPC resources for cost optimization
# This helps identify existing security groups and VPC endpoints that can be reused

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Discovering Existing VPC Resources for Cost Optimization${NC}"
echo "=================================================================="

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI not found. Please install AWS CLI first.${NC}"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

REGION=$(aws configure get region)
if [ -z "$REGION" ]; then
    REGION="us-east-1"
    echo -e "${YELLOW}⚠️  No default region set, using us-east-1${NC}"
fi

echo -e "${GREEN}✅ AWS CLI configured for region: $REGION${NC}"
echo ""

# Function to check if VPC endpoints exist
check_vpc_endpoints() {
    echo -e "${BLUE}🔍 Checking for existing VPC endpoints...${NC}"
    
    # Check for SQS VPC endpoint
    SQS_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.sqs" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")
    
    # Check for Bedrock VPC endpoint
    BEDROCK_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.bedrock" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")
    
    # Check for S3 Gateway endpoint
    S3_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.s3" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")
    
    # Check for DynamoDB Gateway endpoint
    DYNAMODB_ENDPOINT=$(aws ec2 describe-vpc-endpoints \
        --filters "Name=service-name,Values=com.amazonaws.$REGION.dynamodb" \
        --query 'VpcEndpoints[0].VpcEndpointId' \
        --output text 2>/dev/null || echo "None")
    
    echo "SQS VPC Endpoint: $SQS_ENDPOINT"
    echo "Bedrock VPC Endpoint: $BEDROCK_ENDPOINT"
    echo "S3 Gateway Endpoint: $S3_ENDPOINT"
    echo "DynamoDB Gateway Endpoint: $DYNAMODB_ENDPOINT"
    
    # Build comma-separated list of existing endpoints
    EXISTING_ENDPOINTS=""
    if [ "$SQS_ENDPOINT" != "None" ] && [ "$SQS_ENDPOINT" != "null" ]; then
        EXISTING_ENDPOINTS="$SQS_ENDPOINT"
    fi
    if [ "$BEDROCK_ENDPOINT" != "None" ] && [ "$BEDROCK_ENDPOINT" != "null" ]; then
        if [ -n "$EXISTING_ENDPOINTS" ]; then
            EXISTING_ENDPOINTS="$EXISTING_ENDPOINTS,$BEDROCK_ENDPOINT"
        else
            EXISTING_ENDPOINTS="$BEDROCK_ENDPOINT"
        fi
    fi
    
    echo ""
}

# Function to check for existing security groups
check_security_groups() {
    echo -e "${BLUE}🔍 Checking for existing security groups...${NC}"
    
    # Look for Lambda security groups
    LAMBDA_SG=$(aws ec2 describe-security-groups \
        --filters "Name=group-name,Values=*lambda*" \
        --query 'SecurityGroups[0].GroupId' \
        --output text 2>/dev/null || echo "None")
    
    # Look for OpenSearch security groups
    OPENSEARCH_SG=$(aws ec2 describe-security-groups \
        --filters "Name=group-name,Values=*opensearch*,*elasticsearch*" \
        --query 'SecurityGroups[0].GroupId' \
        --output text 2>/dev/null || echo "None")
    
    # Look for VPC endpoint security groups
    VPC_ENDPOINT_SG=$(aws ec2 describe-security-groups \
        --filters "Name=group-name,Values=*endpoint*,*vpce*" \
        --query 'SecurityGroups[0].GroupId' \
        --output text 2>/dev/null || echo "None")
    
    echo "Lambda Security Group: $LAMBDA_SG"
    echo "OpenSearch Security Group: $OPENSEARCH_SG"
    echo "VPC Endpoint Security Group: $VPC_ENDPOINT_SG"
    echo ""
}

# Function to generate export commands
generate_export_commands() {
    echo -e "${BLUE}📋 Generated Export Commands${NC}"
    echo "================================"
    echo ""
    
    if [ -n "$EXISTING_ENDPOINTS" ]; then
        echo -e "${GREEN}# VPC Endpoints (saves ~\$61/month)${NC}"
        echo "export EXISTING_VPC_ENDPOINT_IDS=\"$EXISTING_ENDPOINTS\""
        echo ""
    fi
    
    if [ "$LAMBDA_SG" != "None" ] && [ "$LAMBDA_SG" != "null" ]; then
        echo -e "${GREEN}# Security Groups${NC}"
        echo "export EXISTING_LAMBDA_SG_ID=\"$LAMBDA_SG\""
    fi
    
    if [ "$OPENSEARCH_SG" != "None" ] && [ "$OPENSEARCH_SG" != "null" ]; then
        echo "export EXISTING_OPENSEARCH_SG_ID=\"$OPENSEARCH_SG\""
    fi
    
    if [ "$VPC_ENDPOINT_SG" != "None" ] && [ "$VPC_ENDPOINT_SG" != "null" ]; then
        echo "export EXISTING_VPC_ENDPOINT_SG_ID=\"$VPC_ENDPOINT_SG\""
    fi
    
    echo ""
    echo -e "${YELLOW}💡 Copy the export commands above and run them before deployment${NC}"
    echo -e "${YELLOW}   This will optimize costs by reusing existing resources.${NC}"
}

# Function to show cost savings
show_cost_savings() {
    echo -e "${BLUE}💰 Cost Optimization Summary${NC}"
    echo "============================="
    
    MONTHLY_SAVINGS=0
    
    if [ -n "$EXISTING_ENDPOINTS" ]; then
        ENDPOINT_COUNT=$(echo "$EXISTING_ENDPOINTS" | tr ',' '\n' | wc -l)
        ENDPOINT_SAVINGS=$((ENDPOINT_COUNT * 7))
        MONTHLY_SAVINGS=$((MONTHLY_SAVINGS + ENDPOINT_SAVINGS))
        echo -e "${GREEN}✅ VPC Endpoints: ~\$${ENDPOINT_SAVINGS}/month saved${NC}"
    else
        echo -e "${YELLOW}⚠️  VPC Endpoints: ~\$14/month (new endpoints will be created)${NC}"
    fi
    
    if [ "$LAMBDA_SG" != "None" ] && [ "$LAMBDA_SG" != "null" ]; then
        echo -e "${GREEN}✅ Security Groups: Reusing existing (no additional cost)${NC}"
    else
        echo -e "${YELLOW}⚠️  Security Groups: New groups will be created (no additional cost)${NC}"
    fi
    
    echo ""
    if [ $MONTHLY_SAVINGS -gt 0 ]; then
        echo -e "${GREEN}🎉 Total Monthly Savings: ~\$${MONTHLY_SAVINGS}${NC}"
    else
        echo -e "${YELLOW}💡 No existing resources found. New resources will be created.${NC}"
        echo -e "${YELLOW}   Consider reusing these resources for future deployments.${NC}"
    fi
}

# Main execution
check_vpc_endpoints
check_security_groups
generate_export_commands
show_cost_savings

echo ""
echo -e "${BLUE}🚀 Next Steps${NC}"
echo "============="
echo "1. Copy the export commands above"
echo "2. Run them in your terminal"
echo "3. Deploy with: make deploy ENV=dev"
echo ""
echo -e "${GREEN}✅ Resource discovery complete!${NC}"
