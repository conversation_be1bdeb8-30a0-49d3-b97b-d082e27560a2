"""
Writer Lambda - RAG Query Processing Engine.

This module implements a comprehensive RAG (Retrieval Augmented Generation)
architecture that processes user queries from Slack and generates intelligent
responses using multiple knowledge sources.
"""

import json
import os
import sys
import time
from typing import Any, Dict, List

import boto3
from aws_requests_auth.aws_auth import AWSRequestsAuth
from botocore.exceptions import ClientError
from opensearchpy import OpenSearch, RequestsHttpConnection

# Add layers to path
sys.path.append("/opt/python")

from exceptions import (
    AmazonQException,
    ConfigurationException,
    ConversationStorageException,
    EmbeddingGenerationException,
    OpenSearchException,
    ResponseGenerationException,
    SlackIntegrationException,
    ValidationException,
)
from logger_config import (
    create_correlation_context,
    log_execution_time,
    log_with_context,
    setup_logger,
)

# Local imports
from rag_models import (
    AmazonQResult,
    ConversationContext,
    KnowledgeResult,
    RAGContext,
)
from utils import (
    create_slack_blocks_for_response,
    format_bedrock_response,
    retry_with_exponential_backoff,
    sanitize_user_input,
    send_slack_message,
    validate_environment_variables,
)

# Configure structured logger
logger = setup_logger(__name__)

# Environment variables with validation
REQUIRED_ENV_VARS = [
    "DYNAMODB_TABLE",
    "OPENSEARCH_DOMAIN",
    "BEDROCK_MODEL_ID",
    "EMBEDDING_MODEL_ID",
]

if not validate_environment_variables(REQUIRED_ENV_VARS):
    raise ConfigurationException("Required environment variables missing")

# Configuration
TABLE_NAME = os.environ.get("DYNAMODB_TABLE")
OPENSEARCH_DOMAIN = os.environ.get("OPENSEARCH_DOMAIN")
BEDROCK_MODEL_ID = os.environ.get("BEDROCK_MODEL_ID")
EMBEDDING_MODEL_ID = os.environ.get("EMBEDDING_MODEL_ID")
TTL_DAYS = int(os.environ.get("TTL_DAYS", "7"))
ENVIRONMENT = os.environ.get("ENVIRONMENT", "dev")

# Initialize AWS clients
try:
    dynamodb = boto3.resource("dynamodb")
    bedrock_runtime = boto3.client("bedrock-runtime")
    qbusiness = boto3.client("qbusiness")
    conversation_table = dynamodb.Table(TABLE_NAME)

    # Initialize OpenSearch client
    region = os.environ.get("AWS_REGION", "ap-southeast-2")
    credentials = boto3.Session().get_credentials()
    awsauth = AWSRequestsAuth(credentials, region, "es")

    opensearch_client = OpenSearch(
        hosts=[
            {"host": OPENSEARCH_DOMAIN.replace("https://", ""), "port": 443}
        ],
        http_auth=awsauth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection,
    )
except Exception as e:
    raise ConfigurationException(f"Failed to initialize AWS clients: {str(e)}")


@log_execution_time(logger)
def get_conversation_history(
    conversation_id: str, limit: int = 10
) -> List[Dict[str, Any]]:
    """Retrieve conversation history from DynamoDB with error handling."""
    try:
        correlation_ctx = create_correlation_context(
            conversation_id=conversation_id
        )

        log_with_context(
            logger,
            "info",
            "Retrieving conversation history",
            correlation_ctx,
            limit=limit,
        )

        response = conversation_table.query(
            KeyConditionExpression="conversation_id = :cid",
            ExpressionAttributeValues={":cid": conversation_id},
            ScanIndexForward=False,
            Limit=limit,
        )

        history = response.get("Items", [])

        log_with_context(
            logger,
            "info",
            "Successfully retrieved conversation history",
            correlation_ctx,
            history_count=len(history),
        )

        return history

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        raise ConversationStorageException(
            f"Failed to retrieve conversation history: {str(e)}",
            error_code=error_code,
            context={"conversation_id": conversation_id},
        )
    except Exception as e:
        raise ConversationStorageException(
            f"Unexpected error retrieving conversation history: {str(e)}",
            context={"conversation_id": conversation_id},
        )


@log_execution_time(logger)
def generate_embeddings(text: str) -> List[float]:
    """Generate embeddings using Bedrock Titan model with error handling."""
    try:
        if not text or not text.strip():
            raise ValidationException("Empty text provided for embedding")

        log_with_context(
            logger, "debug", "Generating embeddings", text_length=len(text)
        )

        response = bedrock_runtime.invoke_model(
            modelId=EMBEDDING_MODEL_ID, body=json.dumps({"inputText": text})
        )

        response_body = json.loads(response["body"].read())
        embeddings = response_body.get("embedding", [])

        if not embeddings:
            raise EmbeddingGenerationException(
                "No embeddings returned from Bedrock"
            )

        log_with_context(
            logger,
            "debug",
            "Successfully generated embeddings",
            embedding_dimension=len(embeddings),
        )

        return embeddings

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        raise EmbeddingGenerationException(
            f"Bedrock embedding generation failed: {str(e)}",
            error_code=error_code,
            context={"text_length": len(text)},
        )
    except json.JSONDecodeError as e:
        raise EmbeddingGenerationException(
            f"Failed to parse Bedrock response: {str(e)}"
        )


@log_execution_time(logger)
def query_amazon_q_business(query: str, limit: int = 3) -> List[AmazonQResult]:
    """Query Amazon Q Business with comprehensive error handling."""
    try:
        if not query or not query.strip():
            raise ValidationException("Empty query provided for Amazon Q")

        sanitized_query = sanitize_user_input(query)

        log_with_context(
            logger,
            "info",
            "Querying Amazon Q Business",
            query_length=len(sanitized_query),
            limit=limit,
        )

        response = qbusiness.chat(
            applicationId=os.environ.get("AMAZON_Q_APPLICATION_ID"),
            userMessage=sanitized_query,
            conversationId=None,
            parentMessageId=None,
        )

        results = []

        if "sourceAttributions" in response:
            for attribution in response["sourceAttributions"][:limit]:
                text_segments = attribution.get("textMessageSegments", [{}])
                content = (
                    text_segments[0].get("text", "") if text_segments else ""
                )

                results.append(
                    AmazonQResult(
                        content=content,
                        source=attribution.get("title", "Amazon Q Business"),
                        document_id=attribution.get("citationNumber", ""),
                        relevance_score=1.0,
                        metadata={
                            "url": attribution.get("url", ""),
                            "updated_at": attribution.get("updatedAt", ""),
                            "snippet": attribution.get("snippet", ""),
                        },
                    )
                )

        log_with_context(
            logger,
            "info",
            "Successfully retrieved Amazon Q Business results",
            results_count=len(results),
        )

        return results

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        raise AmazonQException(
            f"Amazon Q Business query failed: {str(e)}",
            error_code=error_code,
            context={"query": query},
        )
    except Exception as e:
        raise AmazonQException(
            f"Unexpected error querying Amazon Q Business: {str(e)}",
            context={"query": query},
        )


@log_execution_time(logger)
def search_knowledge_base(query: str, limit: int = 5) -> List[KnowledgeResult]:
    """Search OpenSearch knowledge base with error handling."""
    try:
        if not query or not query.strip():
            raise ValidationException(
                "Empty query provided for knowledge search"
            )

        log_with_context(
            logger,
            "info",
            "Searching OpenSearch knowledge base",
            query_length=len(query),
            limit=limit,
        )

        # Generate query embeddings
        query_embedding = generate_embeddings(query)

        # Hybrid search: combine keyword and vector search
        search_body = {
            "size": limit,
            "query": {
                "bool": {
                    "should": [
                        {
                            "multi_match": {
                                "query": query,
                                "fields": ["title^2", "content"],
                                "type": "best_fields",
                            }
                        },
                        {
                            "knn": {
                                "content_vector": {
                                    "vector": query_embedding,
                                    "k": limit,
                                }
                            }
                        },
                    ]
                }
            },
            "_source": ["title", "content", "source", "metadata"],
        }

        response = opensearch_client.search(
            index="knowledge-base", body=search_body
        )

        results = []
        for hit in response.get("hits", {}).get("hits", []):
            source_data = hit["_source"]
            results.append(
                KnowledgeResult(
                    content=source_data.get("content", ""),
                    source=source_data.get("source", ""),
                    confidence=hit["_score"],
                    metadata=source_data.get("metadata", {}),
                )
            )

        log_with_context(
            logger,
            "info",
            "Successfully retrieved OpenSearch results",
            results_count=len(results),
        )

        return results

    except EmbeddingGenerationException:
        # Re-raise embedding exceptions
        raise
    except Exception as e:
        raise OpenSearchException(
            f"OpenSearch knowledge base search failed: {str(e)}",
            context={"query": query},
        )


@log_execution_time(logger)
def create_rag_context(
    conversation_context: ConversationContext,
    opensearch_results: List[KnowledgeResult],
    amazon_q_results: List[AmazonQResult],
) -> RAGContext:
    """Create comprehensive RAG context from all knowledge sources."""
    try:
        log_with_context(
            logger,
            "info",
            "Creating RAG context",
            conversation_id=conversation_context.conversation_id,
            opensearch_count=len(opensearch_results),
            amazon_q_count=len(amazon_q_results),
        )

        # Combine knowledge from both sources
        combined_knowledge = ""

        # Add OpenSearch results
        if opensearch_results:
            combined_knowledge += "=== Vector Search Results ===\n"
            for i, result in enumerate(opensearch_results[:3], 1):
                source_info = f"{i}. Source: {result.source}"
                confidence_info = f" (Confidence: {result.confidence:.2f})\n"
                content_preview = f"   Content: {result.content[:500]}...\n\n"
                combined_knowledge += (
                    source_info + confidence_info + content_preview
                )

        # Add Amazon Q Business results
        if amazon_q_results:
            combined_knowledge += "=== Amazon Q Business Results ===\n"
            for i, result in enumerate(amazon_q_results[:2], 1):
                source_info = f"{i}. Source: {result.source}\n"
                content_preview = f"   Content: {result.content[:500]}...\n\n"
                combined_knowledge += source_info + content_preview

        rag_context = RAGContext(
            conversation_context=conversation_context,
            opensearch_results=opensearch_results,
            amazon_q_results=amazon_q_results,
            combined_knowledge=combined_knowledge,
        )

        log_with_context(
            logger,
            "info",
            "Successfully created RAG context",
            conversation_id=conversation_context.conversation_id,
            total_results=rag_context.total_results_count,
            has_knowledge=rag_context.has_knowledge,
        )

        return rag_context

    except Exception as e:
        raise ResponseGenerationException(
            f"Failed to create RAG context: {str(e)}",
            context={
                "conversation_id": conversation_context.conversation_id,
                "opensearch_count": len(opensearch_results),
                "amazon_q_count": len(amazon_q_results),
            },
        )


@log_execution_time(logger)
def generate_response(rag_context: RAGContext) -> str:
    """Generate response using Bedrock Claude with comprehensive context."""
    try:
        context = rag_context.conversation_context

        log_with_context(
            logger,
            "info",
            "Generating response with Bedrock Claude",
            conversation_id=context.conversation_id,
            total_knowledge_results=rag_context.total_results_count,
        )

        # Build conversation history context
        conversation_history = "\n".join(
            [
                f"{'User' if msg.get('role') == 'user' else 'Assistant'}: "
                f"{msg.get('content', '')}"
                for msg in context.previous_messages[-4:]
            ]
        )

        # Create comprehensive prompt
        prompt_parts = [
            "You are an intelligent AI assistant with access to multiple "
            "knowledge sources including:",
            "1. Vector search results from internal documents",
            "2. Amazon Q Business enterprise knowledge",
            "3. Conversation history for context",
            "",
            "Based on the provided information, answer the user's question "
            "accurately and helpfully.",
            "",
            "=== CONVERSATION HISTORY ===",
            conversation_history,
            "",
            "=== KNOWLEDGE SOURCES ===",
            rag_context.combined_knowledge,
            "",
            "=== USER QUESTION ===",
            context.query,
            "",
            "=== INSTRUCTIONS ===",
            "- Provide a comprehensive and accurate answer based on the "
            "available knowledge",
            "- If information is conflicting between sources, acknowledge "
            "this and explain",
            "- If the knowledge sources don't contain relevant information, "
            "state this clearly",
            "- Include relevant citations when referencing specific sources",
            "- Keep the response concise but thorough",
            "- Maintain conversation context and refer to previous exchanges "
            "when relevant",
            "",
            "Please provide your response:",
        ]

        prompt = "\n".join(prompt_parts)

        # Use retry mechanism for Bedrock call
        @retry_with_exponential_backoff
        def call_bedrock():
            return bedrock_runtime.invoke_model(
                modelId=BEDROCK_MODEL_ID,
                body=json.dumps(
                    {
                        "anthropic_version": "bedrock-2023-05-31",
                        "max_tokens": 1500,
                        "temperature": 0.1,
                        "messages": [{"role": "user", "content": prompt}],
                    }
                ),
            )

        response = call_bedrock()
        response_body = json.loads(response["body"].read())
        raw_response = response_body["content"][0]["text"]

        # Format response for Slack
        formatted_response = format_bedrock_response(raw_response)

        log_with_context(
            logger,
            "info",
            "Successfully generated response",
            conversation_id=context.conversation_id,
            response_length=len(formatted_response),
            knowledge_sources_used=rag_context.total_results_count,
        )

        return formatted_response

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        raise ResponseGenerationException(
            f"Bedrock response generation failed: {str(e)}",
            error_code=error_code,
            context={
                "conversation_id": rag_context.conversation_context.conversation_id
            },
        )
    except json.JSONDecodeError as e:
        raise ResponseGenerationException(
            f"Failed to parse Bedrock response: {str(e)}"
        )
    except Exception as e:
        raise ResponseGenerationException(
            f"Unexpected error generating response: {str(e)}",
            context={
                "conversation_id": rag_context.conversation_context.conversation_id
            },
        )


@log_execution_time(logger)
def save_conversation(
    conversation_id: str,
    user_query: str,
    assistant_response: str,
    user_id: str,
) -> bool:
    """Save conversation exchange to DynamoDB with error handling."""
    try:
        correlation_ctx = create_correlation_context(
            conversation_id=conversation_id, user_id=user_id
        )

        log_with_context(
            logger, "info", "Saving conversation to DynamoDB", correlation_ctx
        )

        timestamp = str(int(time.time()))
        ttl = int(time.time()) + (TTL_DAYS * 24 * 60 * 60)

        # Save user message
        conversation_table.put_item(
            Item={
                "conversation_id": conversation_id,
                "timestamp": timestamp + "_user",
                "role": "user",
                "content": user_query,
                "user_id": user_id,
                "ttl": ttl,
            }
        )

        # Save assistant response
        conversation_table.put_item(
            Item={
                "timestamp": timestamp + "_assistant",
                "role": "assistant",
                "content": assistant_response,
                "user_id": user_id,
                "ttl": ttl,
                "conversation_id": conversation_id,
            }
        )

        log_with_context(
            logger, "info", "Successfully saved conversation", correlation_ctx
        )

        return True

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        raise ConversationStorageException(
            f"Failed to save conversation: {str(e)}",
            error_code=error_code,
            context={"conversation_id": conversation_id},
        )
    except Exception as e:
        raise ConversationStorageException(
            f"Unexpected error saving conversation: {str(e)}",
            context={"conversation_id": conversation_id},
        )


@log_execution_time(logger)
def send_slack_response(
    channel_id: str,
    response_text: str,
    thread_ts: str = None,
    knowledge_sources: List[Dict[str, Any]] = None,
) -> bool:
    """Send response back to Slack with rich formatting and error handling."""
    try:
        log_with_context(
            logger,
            "info",
            "Sending response to Slack",
            channel_id=channel_id,
            thread_ts=thread_ts,
            response_length=len(response_text),
            has_sources=bool(knowledge_sources),
        )

        # Create rich message blocks if knowledge sources are provided
        if knowledge_sources:
            blocks = create_slack_blocks_for_response(
                response_text, knowledge_sources
            )
            success = send_slack_message(
                channel_id=channel_id,
                text=response_text,
                thread_ts=thread_ts,
                blocks=blocks,
            )
        else:
            success = send_slack_message(
                channel_id=channel_id, text=response_text, thread_ts=thread_ts
            )

        if success:
            log_with_context(
                logger,
                "info",
                "Successfully sent Slack response",
                channel_id=channel_id,
                thread_ts=thread_ts,
            )
        else:
            raise SlackIntegrationException(
                "Slack API returned failure status"
            )

        return success

    except Exception as e:
        if not isinstance(e, SlackIntegrationException):
            raise SlackIntegrationException(
                f"Failed to send Slack response: {str(e)}",
                context={"channel_id": channel_id, "thread_ts": thread_ts},
            )
        raise


def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Lambda handler for processing messages from SQS with comprehensive error handling."""
    request_id = getattr(context, "aws_request_id", None)

    log_with_context(
        logger,
        "info",
        "Writer Lambda invoked",
        request_id=request_id,
        records_count=len(event.get("Records", [])),
    )

    processed_count = 0
    failed_count = 0
    processing_start = time.time()

    # Process SQS messages
    for record in event.get("Records", []):
        record_start_time = time.time()
        message_id = record.get("messageId", "unknown")

        try:
            # Parse the message
            message_body = json.loads(record["body"])

            # Create conversation context
            conversation_context = ConversationContext(
                conversation_id=message_body["conversation_id"],
                user_id=message_body["user_id"],
                channel_id=message_body["channel_id"],
                query=message_body["query"],
                previous_messages=get_conversation_history(
                    message_body["conversation_id"]
                ),
                timestamp=message_body.get("timestamp", str(int(time.time()))),
            )

            correlation_ctx = create_correlation_context(
                conversation_id=conversation_context.conversation_id,
                user_id=conversation_context.user_id,
                request_id=request_id,
            )

            log_with_context(
                logger,
                "info",
                "Processing query message",
                correlation_ctx,
                query_length=len(conversation_context.query),
                message_id=message_id,
            )

            # Search multiple knowledge sources in parallel
            opensearch_results = search_knowledge_base(
                conversation_context.query
            )
            amazon_q_results = query_amazon_q_business(
                conversation_context.query
            )

            # Create comprehensive RAG context
            rag_context = create_rag_context(
                conversation_context, opensearch_results, amazon_q_results
            )

            # Generate response using enhanced RAG pipeline
            assistant_response = generate_response(rag_context)

            # Save conversation
            save_conversation(
                conversation_context.conversation_id,
                conversation_context.query,
                assistant_response,
                conversation_context.user_id,
            )

            # Prepare knowledge sources for Slack formatting
            knowledge_sources = []
            for result in opensearch_results[:2]:
                knowledge_sources.append(
                    {"source": result.source, "confidence": result.confidence}
                )
            for result in amazon_q_results[:1]:
                knowledge_sources.append(
                    {
                        "source": result.source,
                        "confidence": result.relevance_score,
                    }
                )

            # Send response to Slack with rich formatting
            send_slack_response(
                conversation_context.channel_id,
                assistant_response,
                message_body.get("thread_ts"),
                knowledge_sources if knowledge_sources else None,
            )

            processing_time = time.time() - record_start_time
            processed_count += 1

            log_with_context(
                logger,
                "info",
                "Successfully processed message",
                correlation_ctx,
                processing_time=processing_time,
                message_id=message_id,
            )

        except (KeyError, json.JSONDecodeError) as e:
            failed_count += 1
            log_with_context(
                logger,
                "error",
                "Invalid message format",
                error=str(e),
                error_type=type(e).__name__,
                message_id=message_id,
            )

        except Exception as e:
            failed_count += 1
            log_with_context(
                logger,
                "error",
                "Unexpected error processing message",
                error=str(e),
                error_type=type(e).__name__,
                message_id=message_id,
            )

    total_processing_time = time.time() - processing_start

    log_with_context(
        logger,
        "info",
        "Writer Lambda completed",
        request_id=request_id,
        processed_count=processed_count,
        failed_count=failed_count,
        total_processing_time=total_processing_time,
    )

    return {
        "statusCode": 200,
        "body": json.dumps(
            {
                "status": "success",
                "processed": processed_count,
                "failed": failed_count,
                "processing_time": total_processing_time,
            }
        ),
    }
